from agent.config import openai_api_key
from agent.schema import load_schema_docs
from agent.vectorstore import create_vector_store
from agent.tools import get_schema_retriever_tool
from agent.prompts import system_prompt
from agent.models import State
from langchain_openai.chat_models import ChatOpenAI
from langchain_core.messages import HumanMessage


def main():
    # Load schema documents
    documents = load_schema_docs()
    # Create vector store
    vector_store = create_vector_store(documents)
    # Create retriever tool
    schema_retriever_tool = get_schema_retriever_tool(vector_store)
    # Set up the base model and bind tools
    base_model = ChatOpenAI(model="gpt-4.1", api_key=openai_api_key)
    model_with_tools = base_model.bind_tools(tools=[schema_retriever_tool])

    # Example: ask a question
    messages = [HumanMessage(content="What is the total number of farmers?")]
    response = model_with_tools.invoke(messages)
    print(response)

if __name__ == "__main__":
    main()
