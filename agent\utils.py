import pandas as pd
import os
import uuid
import json
from datetime import datetime


def describe_dataframe(df: pd.DataFrame) -> dict:
    non_numeric_cols = df.select_dtypes(exclude="number").columns
    return {
        "columns": {col: str(dtype) for col, dtype in df.dtypes.items()},
        "nrows": df.shape[0],
        "ncolumns": df.shape[1],
        "sample_rows": df.head(5).to_dict(orient="records"),
        "null_counts": df.isnull().sum().to_dict(),
        "unique_counts": {col: df[col].nunique() for col in non_numeric_cols},
        "sample_values": {col: df[col].dropna().unique()[:5].tolist() for col in non_numeric_cols},
        "numeric_summary": df.select_dtypes(include="number").describe().loc[["min", "mean", "max"]].to_dict(),
        "most_frequent": {col: df[col].value_counts(dropna=True).head(1).to_dict() for col in non_numeric_cols}
    }

class ResultStore:
    def __init__(self, storage_dir: str = "results"):
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)

    def save(self, df: pd.DataFrame, query: str, user_id: str = None, summary: str = "") -> str:
        query_id = str(uuid.uuid4())[:8]
        now = datetime.utcnow().isoformat()
        result_path = os.path.join(self.storage_dir, f"{query_id}.parquet")
        df.to_parquet(result_path, index=False)
        metadata = {
            "query_id": query_id,
            "user_id": user_id,
            "query": query,
            "created_at": now,
            "row_count": len(df),
            "columns": dict(df.dtypes.apply(str)),
            "summary": summary,
            "result_path": result_path
        }
        metadata_path = os.path.join(self.storage_dir, f"{query_id}.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        return query_id

    def load(self, query_id: str) -> pd.DataFrame:
        path = os.path.join(self.storage_dir, f"{query_id}.parquet")
        if not os.path.exists(path):
            raise FileNotFoundError(f"No result found for query_id: {query_id}")
        return pd.read_parquet(path)

    def load_metadata(self, query_id: str) -> dict:
        path = os.path.join(self.storage_dir, f"{query_id}.json")
        if not os.path.exists(path):
            raise FileNotFoundError(f"No metadata found for query_id: {query_id}")
        with open(path) as f:
            return json.load(f)
