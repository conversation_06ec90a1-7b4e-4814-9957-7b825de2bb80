import os
import getpass
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, IPvAnyAddress

from langchain_openai import ChatOpenAI
from models import SQLQuery

def _set_env(key: str):
    if key not in os.environ:
        os.environ[key] = getpass.getpass(f"{key}:")
    return os.environ[key]

class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False)
    POSTGRES_HOST: IPvAnyAddress = Field()
    POSTGRES_PORT: int = Field(...)
    POSTGRES_DATABASE: str = Field(min_length=5)
    POSTGRES_USERNAME: str = Field(min_length=5)
    POSTGRES_PASSWORD: str = Field(...)
    OPENAI_API_KEY: str = Field(...)



settings = Settings()
openai_api_key = settings.OPENAI_API_KEY or _set_env("OPENAI_API_KEY")


class LLMs:
    base_model = ChatOpenAI(model="gpt-4.1", api_key=openai_api_key)
    sql_llm = base_model.with_structured_output(SQLQuery)
    chat_llm = 
