import json
from langchain_core.documents import Document

def format_doc(desc):
    names = desc[0].lstrip("SH_").split('.')
    if names[0] == 'exchange_mart':
        source_system = 'Exchange'
    elif names[0] == 'trade_mart':
        source_system = 'Workbench'
    else:
        source_system = 'Unknown'
    desc = desc[1]
    meta = {'schema': names[0], 'table': names[1], 'source': source_system}
    return Document(page_content=desc, metadata=meta)

def load_schema_docs(path="schema_desc.json"):
    with open(path) as f:
        descs = json.load(f)
    return [format_doc(desc) for desc in descs]
